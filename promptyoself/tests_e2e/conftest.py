# -*- coding: utf-8 -*-
"""E2E test fixtures and configuration."""

import logging
import os
import sys
import threading
import time
from contextlib import contextmanager

import pytest
from playwright.sync_api import sync_playwright

# Add the parent directory to the path to import the app
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app import create_app
from app.database import db as _db


@pytest.fixture(scope="session")
def app():
    """Create application for E2E tests."""
    # Use test configuration
    _app = create_app("tests.settings")
    _app.logger.setLevel(logging.CRITICAL)

    # Create application context
    ctx = _app.app_context()
    ctx.push()

    # Create all tables
    _db.create_all()

    yield _app

    # Cleanup
    _db.drop_all()
    ctx.pop()


@pytest.fixture(scope="session")
def live_server(app):
    """Start a live server for E2E tests."""
    import threading
    from werkzeug.serving import make_server

    # Use a different port for testing
    port = 5001
    server = make_server('127.0.0.1', port, app, threaded=True)

    # Start server in a separate thread
    server_thread = threading.Thread(target=server.serve_forever)
    server_thread.daemon = True
    server_thread.start()

    # Wait a bit for server to start
    time.sleep(1)

    # Provide the server URL
    yield f"http://127.0.0.1:{port}"

    # Shutdown server
    server.shutdown()


@pytest.fixture(scope="session")
def browser():
    """Create a browser instance for E2E tests."""
    with sync_playwright() as p:
        browser = p.chromium.launch(headless=True)
        yield browser
        browser.close()


@pytest.fixture
def page(browser):
    """Create a new page for each test."""
    page = browser.new_page()
    yield page
    page.close()


@pytest.fixture
def db(app):
    """Create database for the tests."""
    _db.app = app
    with app.app_context():
        _db.create_all()

    yield _db

    # Cleanup
    _db.session.close()
    _db.drop_all()
