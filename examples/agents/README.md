# Letta Agent Tools for PromptyoSelf Integration

This directory contains comprehensive tools and examples for integrating Letta agents with the PromptyoSelf system, enabling AI agents to manage projects, tasks, and reminders.

## 🚀 Quick Start

### Prerequisites

1. **Install Dependencies:**
   ```bash
   pip install letta-client requests
   ```

2. **Set Environment Variables:**
   ```bash
   export INTERNAL_AGENT_API_KEY="your_api_key_here"
   export OPENAI_API_KEY="your_openai_key"  # or other LLM provider
   ```

3. **Start Required Services:**
   ```bash
   # Start PromptyoSelf server (in one terminal)
   cd promptyoself
   flask run --port 5000

   # Start Letta server (in another terminal)
   docker run -p 8283:8283 -e OPENAI_API_KEY="your_key" letta/letta:latest
   ```

### Basic Usage

```python
from letta_promptyoself_tools import create_project, create_task, schedule_reminder

# Create a project
result = create_project(name="AI Research", description="Research project")
print(result)  # ✅ Project 'AI Research' created successfully with ID 1

# Create a task
result = create_task(name="Literature Review", project_id=1)
print(result)  # ✅ Task 'Literature Review' created successfully with ID 1

# Schedule a reminder
result = schedule_reminder(
    reminder_text="Start literature review",
    scheduled_for="2024-01-15T09:00:00Z",
    task_id=1,
    process_name="research_workflow"
)
print(result)  # ✅ Reminder scheduled successfully for 2024-01-15T09:00:00Z
```

## 📁 Files Overview

### `letta_promptyoself_tools.py` ⭐ **NEW COMPREHENSIVE TOOLKIT**

A complete set of Letta custom tools providing full PromptyoSelf integration:

**🏗️ Project Management Tools:**
- `create_project()` - Create new projects
- `list_projects()` - List projects with filtering
- `get_project()` - Get project details
- `update_project()` - Update project information
- `delete_project()` - Delete projects

**📋 Task Management Tools:**
- `create_task()` - Create tasks and subtasks
- `list_tasks()` - List tasks with filtering
- `get_task()` - Get task details
- `update_task()` - Update task information
- `delete_task()` - Delete tasks

**⏰ Reminder Management Tools:**
- `schedule_reminder()` - Schedule reminders via internal API
- `create_reminder_via_api()` - Create reminders via standard API
- `list_reminders()` - List all reminders
- `get_reminder()` - Get reminder details
- `update_reminder()` - Update reminder information
- `delete_reminder()` - Delete reminders

**Features:**
- ✅ Complete CRUD operations for all PromptyoSelf entities
- ✅ Comprehensive error handling and validation
- ✅ Detailed success/failure feedback
- ✅ Support for both internal and standard APIs
- ✅ Letta tool metadata for easy registration
- ✅ Pagination and filtering support
- ✅ ISO 8601 datetime validation

### `test_letta_integration.py` 🧪 **INTEGRATION TEST SUITE**

Comprehensive test script demonstrating real-world integration:

**Features:**
- ✅ Automated prerequisite checking
- ✅ Letta agent creation with PromptyoSelf tools
- ✅ End-to-end workflow testing
- ✅ Agent conversation testing
- ✅ Automatic cleanup

**Usage:**
```bash
python test_letta_integration.py
```

### `promptyoself_reminder_tool.py` 📅 **LEGACY SINGLE TOOL**

Original single-purpose tool for reminder scheduling (now superseded by the comprehensive toolkit above).

## 🔧 Integration Guide

### Step 1: Register Tools with Letta Agent

```python
from letta_client import Letta
from letta_promptyoself_tools import LETTA_TOOLS_METADATA

# Initialize Letta client
client = Letta(base_url="http://localhost:8283")

# Create agent
agent = client.agents.create(
    name="PromptyoSelf Assistant",
    description="AI assistant for project and task management"
)

# Register PromptyoSelf tools
# Note: Exact method may vary by Letta version
for tool_metadata in LETTA_TOOLS_METADATA:
    client.tools.create(
        name=tool_metadata["name"],
        description=tool_metadata["description"],
        parameters=tool_metadata["parameters"]
    )
```

### Step 2: Test the Integration

```python
# Run the comprehensive test suite
python test_letta_integration.py
```

### Step 3: Use in Production

```python
# Send messages to your agent
response = client.agents.messages.create(
    agent_id=agent.id,
    messages=[{
        "role": "user",
        "content": "Create a project called 'Website Redesign' and add a task for 'User Research'"
    }]
)
```

## 🎯 Use Cases

### 1. **Project Management Assistant**
```python
# Agent can help manage projects
"Create a new project for our Q1 marketing campaign"
"List all projects that contain 'marketing' in the name"
"Update the project description for project ID 5"
```

### 2. **Task Scheduling Assistant**
```python
# Agent can manage tasks and subtasks
"Create a task called 'Design Review' under the Website project"
"Break down the 'User Research' task into subtasks"
"Show me all tasks for project ID 3"
```

### 3. **Reminder Scheduling Assistant**
```python
# Agent can schedule intelligent reminders
"Remind me to review the design mockups tomorrow at 2 PM"
"Set up a weekly reminder for team standup meetings"
"Schedule a reminder for the project deadline next Friday"
```

### 4. **Workflow Automation**
```python
# Agent can automate complex workflows
"When I create a new project, automatically create standard tasks like 'Planning', 'Development', and 'Testing'"
"Set up reminders for all project milestones"
"Create a complete project structure for a software development project"
```

## 🔍 Testing Scenarios

### Scenario 1: Basic CRUD Operations
```bash
# Test all basic operations
python -c "
from letta_promptyoself_tools import *
print(create_project('Test Project'))
print(list_projects())
print(create_task('Test Task', 1))
print(list_tasks())
print(schedule_reminder('Test reminder', '2024-12-31T23:59:00Z', 1, 'test'))
print(list_reminders())
"
```

### Scenario 2: Error Handling
```bash
# Test error conditions
python -c "
from letta_promptyoself_tools import *
print(create_project(''))  # Should fail - empty name
print(create_task('Task', 999))  # Should fail - invalid project ID
print(schedule_reminder('', '2024-12-31T23:59:00Z', 1, 'test'))  # Should fail - empty text
"
```

### Scenario 3: Integration Test
```bash
# Run full integration test
python test_letta_integration.py
```

## 🛠️ Configuration

### Environment Variables

| Variable | Required | Description | Example |
|----------|----------|-------------|---------|
| `INTERNAL_AGENT_API_KEY` | Yes | API key for PromptyoSelf internal API | `test-api-key-123` |
| `OPENAI_API_KEY` | Yes* | OpenAI API key for Letta | `sk-...` |
| `ANTHROPIC_API_KEY` | Yes* | Anthropic API key for Letta | `sk-ant-...` |

*One LLM provider API key is required

### Server Configuration

| Service | Default URL | Port | Notes |
|---------|-------------|------|-------|
| PromptyoSelf | `http://localhost:5000` | 5000 | Flask development server |
| Letta | `http://localhost:8283` | 8283 | Docker or pip installation |

## 🐛 Troubleshooting

### Common Issues

1. **"INTERNAL_AGENT_API_KEY is not set"**
   ```bash
   export INTERNAL_AGENT_API_KEY="test-api-key-123"
   ```

2. **"Failed to connect to PromptyoSelf server"**
   ```bash
   # Check if PromptyoSelf is running
   curl http://localhost:5000/api/projects
   ```

3. **"Failed to connect to Letta server"**
   ```bash
   # Check if Letta is running
   curl http://localhost:8283/v1/agents
   ```

4. **"Invalid datetime format"**
   ```python
   # Use ISO 8601 format
   scheduled_for = "2024-01-15T10:30:00Z"  # ✅ Correct
   scheduled_for = "2024-01-15 10:30:00"   # ❌ Wrong
   ```

### Debug Mode

Enable debug logging:
```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

## 📚 API Reference

### Tool Categories

1. **Project Tools** (5 tools)
   - Full CRUD operations for projects
   - Filtering and pagination support

2. **Task Tools** (5 tools)
   - Full CRUD operations for tasks
   - Support for subtasks and project relationships

3. **Reminder Tools** (6 tools)
   - Two creation methods (internal API + standard API)
   - Full CRUD operations with recurrence support

### Return Format

All tools return human-readable strings with status indicators:
- ✅ Success operations
- ❌ Error conditions
- 📋 Information displays
- ⚠️ Warnings

## 🤝 Contributing

To add new tools or improve existing ones:

1. Add the function to `letta_promptyoself_tools.py`
2. Add corresponding metadata to `LETTA_TOOLS_METADATA`
3. Add tests to `test_letta_integration.py`
4. Update this README

## 📄 License

This integration toolkit is part of the PromptyoSelf project and follows the same license terms.
```

2. **Import and use in Letta agent:**
   ```python
   from promptyoself_reminder_tool import schedule_promptyoself_reminder

   # Schedule a self-prompt
   result = schedule_promptyoself_reminder(
       reminder_text="Follow up on project status",
       scheduled_for="2024-01-15T10:30:00Z",
       process_name="project_management",
       agent_id="my_agent"
   )
   ```

3. **Test the tool standalone:**
   ```bash
   cd examples/agents
   python promptyoself_reminder_tool.py
   ```

**Parameters:**
- `reminder_text` (required): The self-prompt message content
- `scheduled_for` (required): ISO 8601 datetime string (e.g., "2024-01-15T10:30:00Z")
- `process_name` (required): Name of the process scheduling the self-prompt
- `agent_id` (optional): Agent identifier (defaults to "letta_agent")
- `promptyoself_port` (optional): PromptyoSelf server port (defaults to 5000)

**Returns:**
JSON string with either success confirmation including self-prompt ID, or error details.

## Requirements

- `requests` library for HTTP calls
- `INTERNAL_AGENT_API_KEY` environment variable
- PromptyoSelf server running and accessible

## API Integration

The tool integrates with the PromptyoSelf internal API endpoint:
- **Endpoint:** `POST /api/internal/agents/reminders`
- **Authentication:** `X-Agent-API-Key` header
- **Content-Type:** `application/json`

For more details about the API, see the PromptyoSelf documentation.