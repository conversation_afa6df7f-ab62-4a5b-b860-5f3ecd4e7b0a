#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Letta-PromptyoSelf Integration Test Script

This script demonstrates how to integrate Letta agents with PromptyoSelf
using the custom tools. It provides examples of:

1. Setting up a Letta client
2. Creating an agent with PromptyoSelf tools
3. Testing the integration with real workflows
4. Demonstrating bidirectional communication

Requirements:
    - letta-client (pip install letta-client)
    - PromptyoSelf server running on localhost:5000
    - Letta server running on localhost:8283
    - INTERNAL_AGENT_API_KEY environment variable set
    - OPENAI_API_KEY or other LLM provider API key set
"""

import os
import sys
import json
import time
from datetime import datetime, timedelta
from typing import Optional

# Add the current directory to Python path to import our tools
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from letta_client import Letta
    from letta_promptyoself_tools import (
        create_project, list_projects, create_task, list_tasks,
        schedule_reminder, list_reminders, LETTA_TOOLS_METADATA
    )
except ImportError as e:
    print(f"❌ Import error: {e}")
    print("Please install required packages:")
    print("  pip install letta-client requests")
    sys.exit(1)


class LettaPromptyoSelfIntegration:
    """
    Integration class for testing Letta agents with PromptyoSelf tools.
    """
    
    def __init__(
        self,
        letta_base_url: str = "http://localhost:8283",
        promptyoself_port: int = 5000,
        letta_token: Optional[str] = None
    ):
        """
        Initialize the integration test environment.
        
        Args:
            letta_base_url: Base URL for Letta server
            promptyoself_port: Port for PromptyoSelf server
            letta_token: Authentication token for Letta (if required)
        """
        self.letta_base_url = letta_base_url
        self.promptyoself_port = promptyoself_port
        self.letta_client = None
        self.agent_id = None
        
        # Initialize Letta client
        try:
            if letta_token:
                self.letta_client = Letta(base_url=letta_base_url, token=letta_token)
            else:
                self.letta_client = Letta(base_url=letta_base_url)
            print(f"✅ Connected to Letta server at {letta_base_url}")
        except Exception as e:
            print(f"❌ Failed to connect to Letta server: {e}")
            raise
    
    def check_prerequisites(self) -> bool:
        """
        Check if all prerequisites are met for the integration test.
        
        Returns:
            bool: True if all prerequisites are met
        """
        print("🔍 Checking prerequisites...")
        
        # Check environment variables
        api_key = os.getenv("INTERNAL_AGENT_API_KEY")
        if not api_key:
            print("❌ INTERNAL_AGENT_API_KEY environment variable is not set")
            return False
        print("✅ INTERNAL_AGENT_API_KEY is configured")
        
        # Check if PromptyoSelf is accessible
        try:
            result = list_projects(promptyoself_port=self.promptyoself_port)
            if "Failed to list projects" in result:
                print(f"❌ PromptyoSelf server not accessible on port {self.promptyoself_port}")
                return False
            print(f"✅ PromptyoSelf server is accessible on port {self.promptyoself_port}")
        except Exception as e:
            print(f"❌ Error connecting to PromptyoSelf: {e}")
            return False
        
        # Check Letta client
        try:
            # Try to list agents to verify connection
            agents = self.letta_client.agents.list()
            print(f"✅ Letta server is accessible ({len(agents)} existing agents)")
        except Exception as e:
            print(f"❌ Error connecting to Letta server: {e}")
            return False
        
        return True
    
    def create_test_agent(self, agent_name: str = "PromptyoSelf-Assistant") -> str:
        """
        Create a Letta agent with PromptyoSelf tools.
        
        Args:
            agent_name: Name for the new agent
            
        Returns:
            str: Agent ID of the created agent
        """
        print(f"🤖 Creating Letta agent: {agent_name}")
        
        try:
            # Create agent with basic configuration
            agent = self.letta_client.agents.create(
                name=agent_name,
                description="An AI assistant that can manage projects, tasks, and reminders using PromptyoSelf"
            )
            
            self.agent_id = agent.id
            print(f"✅ Agent created with ID: {self.agent_id}")
            
            # TODO: Register custom tools with the agent
            # Note: The exact method for registering tools may vary depending on Letta version
            # This would typically involve calling something like:
            # self.letta_client.agents.add_tools(agent_id=self.agent_id, tools=LETTA_TOOLS_METADATA)
            
            return self.agent_id
            
        except Exception as e:
            print(f"❌ Failed to create agent: {e}")
            raise
    
    def test_basic_workflow(self) -> bool:
        """
        Test a basic workflow: create project, create task, schedule reminder.
        
        Returns:
            bool: True if workflow completed successfully
        """
        print("\n🧪 Testing basic workflow...")
        
        try:
            # Step 1: Create a test project
            print("1️⃣ Creating test project...")
            project_result = create_project(
                name="Letta Integration Test",
                description="Test project for Letta-PromptyoSelf integration",
                promptyoself_port=self.promptyoself_port
            )
            print(f"   {project_result}")
            
            if "❌" in project_result:
                return False
            
            # Step 2: List projects to verify creation
            print("2️⃣ Listing projects...")
            projects_result = list_projects(promptyoself_port=self.promptyoself_port)
            print(f"   {projects_result}")
            
            # Step 3: Create a test task
            print("3️⃣ Creating test task...")
            task_result = create_task(
                name="Test Letta Integration",
                project_id=1,  # Assuming this is the first project
                description="Test task for verifying Letta integration",
                promptyoself_port=self.promptyoself_port
            )
            print(f"   {task_result}")
            
            if "❌" in task_result:
                return False
            
            # Step 4: Schedule a reminder
            print("4️⃣ Scheduling test reminder...")
            future_time = (datetime.now() + timedelta(minutes=5)).isoformat() + "Z"
            reminder_result = schedule_reminder(
                reminder_text="Test reminder from Letta agent",
                scheduled_for=future_time,
                task_id=1,  # Assuming this is the first task
                process_name="letta_integration_test",
                agent_id="test_agent",
                promptyoself_port=self.promptyoself_port
            )
            print(f"   {reminder_result}")
            
            if "❌" in reminder_result:
                return False
            
            # Step 5: List reminders to verify creation
            print("5️⃣ Listing reminders...")
            reminders_result = list_reminders(promptyoself_port=self.promptyoself_port)
            print(f"   {reminders_result}")
            
            print("✅ Basic workflow completed successfully!")
            return True
            
        except Exception as e:
            print(f"❌ Workflow failed: {e}")
            return False
    
    def test_agent_conversation(self) -> bool:
        """
        Test a conversation with the Letta agent using PromptyoSelf tools.
        
        Returns:
            bool: True if conversation test completed successfully
        """
        if not self.agent_id:
            print("❌ No agent created yet. Call create_test_agent() first.")
            return False
        
        print(f"\n💬 Testing conversation with agent {self.agent_id}...")
        
        try:
            # Send a message to the agent asking it to create a project
            message = "Please create a new project called 'AI Research' with description 'Research on artificial intelligence applications'"
            
            print(f"👤 User: {message}")
            
            # Note: The exact method for sending messages may vary depending on Letta version
            # This is a placeholder for the actual implementation
            response = self.letta_client.agents.messages.create(
                agent_id=self.agent_id,
                messages=[{
                    "role": "user",
                    "content": message
                }]
            )
            
            print(f"🤖 Agent: {response}")
            
            # TODO: Verify that the agent actually used the PromptyoSelf tools
            # This would involve checking the agent's tool usage logs or the PromptyoSelf database
            
            return True
            
        except Exception as e:
            print(f"❌ Conversation test failed: {e}")
            return False
    
    def cleanup(self):
        """
        Clean up test resources.
        """
        print("\n🧹 Cleaning up...")
        
        if self.agent_id and self.letta_client:
            try:
                self.letta_client.agents.delete(agent_id=self.agent_id)
                print(f"✅ Deleted agent {self.agent_id}")
            except Exception as e:
                print(f"⚠️  Failed to delete agent: {e}")
        
        print("✅ Cleanup completed")


def main():
    """
    Main function to run the integration tests.
    """
    print("🚀 Letta-PromptyoSelf Integration Test")
    print("=" * 50)
    
    # Initialize integration test
    integration = LettaPromptyoSelfIntegration()
    
    try:
        # Check prerequisites
        if not integration.check_prerequisites():
            print("❌ Prerequisites not met. Please check your setup.")
            return False
        
        # Create test agent
        agent_id = integration.create_test_agent()
        
        # Run basic workflow test
        workflow_success = integration.test_basic_workflow()
        
        # Run conversation test
        conversation_success = integration.test_agent_conversation()
        
        # Summary
        print("\n📊 Test Results Summary:")
        print(f"   Prerequisites: ✅")
        print(f"   Agent Creation: ✅")
        print(f"   Basic Workflow: {'✅' if workflow_success else '❌'}")
        print(f"   Agent Conversation: {'✅' if conversation_success else '❌'}")
        
        overall_success = workflow_success and conversation_success
        print(f"\n🎯 Overall Result: {'✅ SUCCESS' if overall_success else '❌ FAILED'}")
        
        return overall_success
        
    except Exception as e:
        print(f"❌ Integration test failed: {e}")
        return False
    
    finally:
        # Always cleanup
        integration.cleanup()


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
