#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Simple Example Usage of PromptyoSelf Letta Tools

This script demonstrates basic usage of the PromptyoSelf tools
without requiring a full Letta server setup. Perfect for testing
individual tools and understanding the API.

Usage:
    python example_usage.py
"""

import os
import sys
from datetime import datetime, timedelta

# Add the current directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from letta_promptyoself_tools import (
    create_project, list_projects, get_project, update_project,
    create_task, list_tasks, get_task, update_task,
    schedule_reminder, list_reminders, get_reminder,
    LETTA_TOOLS_METADATA
)


def print_section(title: str):
    """Print a formatted section header."""
    print(f"\n{'='*60}")
    print(f"  {title}")
    print(f"{'='*60}")


def print_step(step: str, description: str):
    """Print a formatted step."""
    print(f"\n{step} {description}")
    print("-" * 50)


def demonstrate_project_management():
    """Demonstrate project management tools."""
    print_section("PROJECT MANAGEMENT DEMO")
    
    # Step 1: Create a project
    print_step("1️⃣", "Creating a new project")
    result = create_project(
        name="Demo Project",
        description="A demonstration project for testing Letta tools"
    )
    print(result)
    
    # Step 2: List projects
    print_step("2️⃣", "Listing all projects")
    result = list_projects()
    print(result)
    
    # Step 3: Get specific project (assuming ID 1)
    print_step("3️⃣", "Getting project details")
    result = get_project(project_id=1)
    print(result)
    
    # Step 4: Update project
    print_step("4️⃣", "Updating project description")
    result = update_project(
        project_id=1,
        description="Updated description for the demo project"
    )
    print(result)


def demonstrate_task_management():
    """Demonstrate task management tools."""
    print_section("TASK MANAGEMENT DEMO")
    
    # Step 1: Create a main task
    print_step("1️⃣", "Creating a main task")
    result = create_task(
        name="Main Task",
        project_id=1,
        description="This is the main task for our demo"
    )
    print(result)
    
    # Step 2: Create a subtask
    print_step("2️⃣", "Creating a subtask")
    result = create_task(
        name="Subtask 1",
        project_id=1,
        description="This is a subtask under the main task",
        parent_task_id=1
    )
    print(result)
    
    # Step 3: List tasks
    print_step("3️⃣", "Listing all tasks")
    result = list_tasks()
    print(result)
    
    # Step 4: List tasks for specific project
    print_step("4️⃣", "Listing tasks for project 1")
    result = list_tasks(project_id_filter=1)
    print(result)
    
    # Step 5: Get specific task
    print_step("5️⃣", "Getting task details")
    result = get_task(task_id=1)
    print(result)


def demonstrate_reminder_management():
    """Demonstrate reminder management tools."""
    print_section("REMINDER MANAGEMENT DEMO")
    
    # Check if API key is set
    api_key = os.getenv("INTERNAL_AGENT_API_KEY")
    if not api_key:
        print("⚠️  INTERNAL_AGENT_API_KEY not set. Setting a test key for demo...")
        os.environ["INTERNAL_AGENT_API_KEY"] = "test-api-key-123"
    
    # Step 1: Schedule a reminder using internal API
    print_step("1️⃣", "Scheduling a reminder (internal API)")
    future_time = (datetime.now() + timedelta(hours=1)).isoformat() + "Z"
    result = schedule_reminder(
        reminder_text="Demo reminder from Letta tools",
        scheduled_for=future_time,
        task_id=1,
        process_name="demo_process",
        agent_id="demo_agent"
    )
    print(result)
    
    # Step 2: List reminders
    print_step("2️⃣", "Listing all reminders")
    result = list_reminders()
    print(result)
    
    # Step 3: Get specific reminder (assuming ID 1)
    print_step("3️⃣", "Getting reminder details")
    result = get_reminder(reminder_id=1)
    print(result)


def demonstrate_error_handling():
    """Demonstrate error handling in tools."""
    print_section("ERROR HANDLING DEMO")
    
    # Test various error conditions
    print_step("1️⃣", "Testing empty project name")
    result = create_project(name="")
    print(result)
    
    print_step("2️⃣", "Testing invalid project ID")
    result = get_project(project_id=999)
    print(result)
    
    print_step("3️⃣", "Testing invalid task creation")
    result = create_task(name="Test", project_id=999)
    print(result)
    
    print_step("4️⃣", "Testing invalid datetime format")
    result = schedule_reminder(
        reminder_text="Test",
        scheduled_for="invalid-date",
        task_id=1,
        process_name="test"
    )
    print(result)


def show_tool_metadata():
    """Display information about available tools."""
    print_section("AVAILABLE LETTA TOOLS")
    
    print(f"📊 Total Tools Available: {len(LETTA_TOOLS_METADATA)}")
    print("\n🔧 Tool Categories:")
    
    categories = {
        "Project Management": [t for t in LETTA_TOOLS_METADATA if "project" in t["name"]],
        "Task Management": [t for t in LETTA_TOOLS_METADATA if "task" in t["name"]],
        "Reminder Management": [t for t in LETTA_TOOLS_METADATA if "reminder" in t["name"]]
    }
    
    for category, tools in categories.items():
        print(f"\n📁 {category} ({len(tools)} tools):")
        for tool in tools:
            print(f"   • {tool['name']} - {tool['description']}")


def main():
    """Main demonstration function."""
    print("🚀 PromptyoSelf Letta Tools - Example Usage")
    print("This demo shows how to use the tools individually")
    
    # Show available tools
    show_tool_metadata()
    
    # Check if PromptyoSelf server is running
    print_section("CONNECTIVITY CHECK")
    result = list_projects()
    if "Failed to list projects" in result:
        print("❌ PromptyoSelf server is not accessible!")
        print("Please start the PromptyoSelf server:")
        print("  cd promptyoself && flask run --port 5000")
        return False
    else:
        print("✅ PromptyoSelf server is accessible")
    
    # Run demonstrations
    try:
        demonstrate_project_management()
        demonstrate_task_management()
        demonstrate_reminder_management()
        demonstrate_error_handling()
        
        print_section("DEMO COMPLETED SUCCESSFULLY")
        print("✅ All demonstrations completed!")
        print("\n💡 Next Steps:")
        print("1. Try modifying the examples above")
        print("2. Run the full integration test: python test_letta_integration.py")
        print("3. Set up a Letta agent with these tools")
        
        return True
        
    except Exception as e:
        print(f"\n❌ Demo failed with error: {e}")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
