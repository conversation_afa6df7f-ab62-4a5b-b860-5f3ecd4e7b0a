#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Letta Custom Tools for PromptyoSelf Integration

This module provides a comprehensive set of Letta custom tools that allow agents
to interact with the PromptyoSelf system for managing projects, tasks, and reminders.

Usage:
    Import these tools and register them with your Letta agent to enable
    full PromptyoSelf integration capabilities.

Requirements:
    - requests library for HTTP calls
    - INTERNAL_AGENT_API_KEY environment variable set (for reminder scheduling)
    - PromptyoSelf server running and accessible
"""

import os
import requests
from datetime import datetime
from typing import Optional, Dict, Any


# Configuration
DEFAULT_PROMPTYOSELF_PORT = 5000
DEFAULT_BASE_URL = "http://localhost"


def get_base_url(port: Optional[int] = None) -> str:
    """Get the base URL for PromptyoSelf API calls."""
    port = port or DEFAULT_PROMPTYOSELF_PORT
    return f"{DEFAULT_BASE_URL}:{port}"


def make_api_request(
    endpoint: str,
    method: str = "GET",
    data: Optional[Dict] = None,
    headers: Optional[Dict] = None,
    port: Optional[int] = None
) -> Dict[str, Any]:
    """
    Make an API request to PromptyoSelf.
    
    Args:
        endpoint: API endpoint path (e.g., "/api/projects")
        method: HTTP method (GET, POST, PUT, DELETE)
        data: Request payload for POST/PUT requests
        headers: Additional headers
        port: PromptyoSelf server port
        
    Returns:
        Dict containing the API response
    """
    base_url = get_base_url(port)
    url = f"{base_url}{endpoint}"
    
    default_headers = {"Content-Type": "application/json"}
    if headers:
        default_headers.update(headers)
    
    try:
        response = requests.request(
            method=method,
            url=url,
            json=data if data else None,
            headers=default_headers,
            timeout=10
        )
        
        if response.status_code >= 400:
            return {
                "success": False,
                "error": f"HTTP {response.status_code}: {response.text}",
                "status_code": response.status_code
            }
        
        return {
            "success": True,
            "data": response.json() if response.content else {},
            "status_code": response.status_code
        }
        
    except requests.exceptions.RequestException as e:
        return {
            "success": False,
            "error": f"Request failed: {str(e)}",
            "status_code": None
        }


# ============================================================================
# PROJECT MANAGEMENT TOOLS
# ============================================================================

def create_project(
    name: str,
    description: str = "",
    promptyoself_port: Optional[int] = None
) -> str:
    """
    Create a new project in PromptyoSelf.
    
    Args:
        name: Project name (required)
        description: Project description (optional)
        promptyoself_port: PromptyoSelf server port (default: 5000)
        
    Returns:
        String describing the result of the operation
    """
    if not name or not name.strip():
        return "❌ Error: Project name is required and cannot be empty"
    
    payload = {
        "name": name.strip(),
        "description": description.strip() if description else ""
    }
    
    result = make_api_request("/api/projects", "POST", payload, port=promptyoself_port)
    
    if result["success"]:
        project_data = result["data"]
        return f"✅ Project '{name}' created successfully with ID {project_data['project']['id']}"
    else:
        return f"❌ Failed to create project: {result['error']}"


def list_projects(
    name_filter: Optional[str] = None,
    page: int = 1,
    per_page: int = 10,
    promptyoself_port: Optional[int] = None
) -> str:
    """
    List projects from PromptyoSelf with optional filtering.
    
    Args:
        name_filter: Filter projects by name (optional)
        page: Page number for pagination (default: 1)
        per_page: Items per page (default: 10)
        promptyoself_port: PromptyoSelf server port (default: 5000)
        
    Returns:
        String containing the list of projects
    """
    params = {"page": page, "per_page": per_page}
    if name_filter:
        params["name"] = name_filter
    
    # Convert params to query string
    query_string = "&".join([f"{k}={v}" for k, v in params.items()])
    endpoint = f"/api/projects?{query_string}"
    
    result = make_api_request(endpoint, port=promptyoself_port)
    
    if result["success"]:
        data = result["data"]
        projects = data.get("projects", [])
        
        if not projects:
            return "📋 No projects found"
        
        project_list = []
        for project in projects:
            project_list.append(
                f"• ID: {project['id']} | Name: {project['name']} | "
                f"Description: {project.get('description', 'N/A')}"
            )
        
        total = data.get("total", len(projects))
        return f"📋 Found {total} project(s):\n" + "\n".join(project_list)
    else:
        return f"❌ Failed to list projects: {result['error']}"


def get_project(
    project_id: int,
    promptyoself_port: Optional[int] = None
) -> str:
    """
    Get details of a specific project.
    
    Args:
        project_id: ID of the project to retrieve
        promptyoself_port: PromptyoSelf server port (default: 5000)
        
    Returns:
        String containing project details
    """
    result = make_api_request(f"/api/projects/{project_id}", port=promptyoself_port)
    
    if result["success"]:
        project = result["data"]["project"]
        return (
            f"📋 Project Details:\n"
            f"• ID: {project['id']}\n"
            f"• Name: {project['name']}\n"
            f"• Description: {project.get('description', 'N/A')}\n"
            f"• Created: {project['created_at']}\n"
            f"• Updated: {project['updated_at']}"
        )
    else:
        return f"❌ Failed to get project: {result['error']}"


def update_project(
    project_id: int,
    name: Optional[str] = None,
    description: Optional[str] = None,
    promptyoself_port: Optional[int] = None
) -> str:
    """
    Update an existing project.
    
    Args:
        project_id: ID of the project to update
        name: New project name (optional)
        description: New project description (optional)
        promptyoself_port: PromptyoSelf server port (default: 5000)
        
    Returns:
        String describing the result of the operation
    """
    if not name and description is None:
        return "❌ Error: At least one field (name or description) must be provided for update"
    
    payload = {}
    if name:
        payload["name"] = name.strip()
    if description is not None:
        payload["description"] = description.strip()
    
    result = make_api_request(f"/api/projects/{project_id}", "PUT", payload, port=promptyoself_port)
    
    if result["success"]:
        return f"✅ Project {project_id} updated successfully"
    else:
        return f"❌ Failed to update project: {result['error']}"


def delete_project(
    project_id: int,
    promptyoself_port: Optional[int] = None
) -> str:
    """
    Delete a project from PromptyoSelf.
    
    Args:
        project_id: ID of the project to delete
        promptyoself_port: PromptyoSelf server port (default: 5000)
        
    Returns:
        String describing the result of the operation
    """
    result = make_api_request(f"/api/projects/{project_id}", "DELETE", port=promptyoself_port)
    
    if result["success"]:
        return f"✅ Project {project_id} deleted successfully"
    else:
        return f"❌ Failed to delete project: {result['error']}"


# ============================================================================
# TASK MANAGEMENT TOOLS
# ============================================================================

def create_task(
    name: str,
    project_id: int,
    description: str = "",
    parent_task_id: Optional[int] = None,
    promptyoself_port: Optional[int] = None
) -> str:
    """
    Create a new task in PromptyoSelf.

    Args:
        name: Task name (required)
        project_id: ID of the project this task belongs to (required)
        description: Task description (optional)
        parent_task_id: ID of parent task for subtasks (optional)
        promptyoself_port: PromptyoSelf server port (default: 5000)

    Returns:
        String describing the result of the operation
    """
    if not name or not name.strip():
        return "❌ Error: Task name is required and cannot be empty"

    if not project_id:
        return "❌ Error: Project ID is required"

    payload = {
        "name": name.strip(),
        "description": description.strip() if description else "",
        "project_id": project_id
    }

    if parent_task_id:
        payload["parent_task_id"] = parent_task_id

    result = make_api_request("/api/tasks", "POST", payload, port=promptyoself_port)

    if result["success"]:
        task_data = result["data"]
        return f"✅ Task '{name}' created successfully with ID {task_data['task']['id']}"
    else:
        return f"❌ Failed to create task: {result['error']}"


def list_tasks(
    name_filter: Optional[str] = None,
    project_id_filter: Optional[int] = None,
    page: int = 1,
    per_page: int = 10,
    promptyoself_port: Optional[int] = None
) -> str:
    """
    List tasks from PromptyoSelf with optional filtering.

    Args:
        name_filter: Filter tasks by name (optional)
        project_id_filter: Filter tasks by project ID (optional)
        page: Page number for pagination (default: 1)
        per_page: Items per page (default: 10)
        promptyoself_port: PromptyoSelf server port (default: 5000)

    Returns:
        String containing the list of tasks
    """
    params = {"page": page, "per_page": per_page}
    if name_filter:
        params["name"] = name_filter
    if project_id_filter:
        params["project_id"] = project_id_filter

    # Convert params to query string
    query_string = "&".join([f"{k}={v}" for k, v in params.items()])
    endpoint = f"/api/tasks?{query_string}"

    result = make_api_request(endpoint, port=promptyoself_port)

    if result["success"]:
        data = result["data"]
        tasks = data.get("tasks", [])

        if not tasks:
            return "📋 No tasks found"

        task_list = []
        for task in tasks:
            parent_info = f" (Parent: {task['parent_task_name']})" if task.get('parent_task_name') else ""
            task_list.append(
                f"• ID: {task['id']} | Name: {task['name']} | "
                f"Project: {task.get('project_name', 'N/A')}{parent_info}"
            )

        total = data.get("total", len(tasks))
        return f"📋 Found {total} task(s):\n" + "\n".join(task_list)
    else:
        return f"❌ Failed to list tasks: {result['error']}"


def get_task(
    task_id: int,
    promptyoself_port: Optional[int] = None
) -> str:
    """
    Get details of a specific task.

    Args:
        task_id: ID of the task to retrieve
        promptyoself_port: PromptyoSelf server port (default: 5000)

    Returns:
        String containing task details
    """
    result = make_api_request(f"/api/tasks/{task_id}", port=promptyoself_port)

    if result["success"]:
        task = result["data"]["task"]
        parent_info = f"\n• Parent Task: {task['parent_task_name']} (ID: {task['parent_task_id']})" if task.get('parent_task_id') else ""
        return (
            f"📋 Task Details:\n"
            f"• ID: {task['id']}\n"
            f"• Name: {task['name']}\n"
            f"• Description: {task.get('description', 'N/A')}\n"
            f"• Project: {task.get('project_name', 'N/A')} (ID: {task['project_id']}){parent_info}\n"
            f"• Created: {task['created_at']}\n"
            f"• Updated: {task['updated_at']}"
        )
    else:
        return f"❌ Failed to get task: {result['error']}"


def update_task(
    task_id: int,
    name: Optional[str] = None,
    description: Optional[str] = None,
    project_id: Optional[int] = None,
    parent_task_id: Optional[int] = None,
    promptyoself_port: Optional[int] = None
) -> str:
    """
    Update an existing task.

    Args:
        task_id: ID of the task to update
        name: New task name (optional)
        description: New task description (optional)
        project_id: New project ID (optional)
        parent_task_id: New parent task ID (optional)
        promptyoself_port: PromptyoSelf server port (default: 5000)

    Returns:
        String describing the result of the operation
    """
    if not any([name, description is not None, project_id, parent_task_id is not None]):
        return "❌ Error: At least one field must be provided for update"

    payload = {}
    if name:
        payload["name"] = name.strip()
    if description is not None:
        payload["description"] = description.strip()
    if project_id:
        payload["project_id"] = project_id
    if parent_task_id is not None:
        payload["parent_task_id"] = parent_task_id

    result = make_api_request(f"/api/tasks/{task_id}", "PUT", payload, port=promptyoself_port)

    if result["success"]:
        return f"✅ Task {task_id} updated successfully"
    else:
        return f"❌ Failed to update task: {result['error']}"


def delete_task(
    task_id: int,
    promptyoself_port: Optional[int] = None
) -> str:
    """
    Delete a task from PromptyoSelf.

    Args:
        task_id: ID of the task to delete
        promptyoself_port: PromptyoSelf server port (default: 5000)

    Returns:
        String describing the result of the operation
    """
    result = make_api_request(f"/api/tasks/{task_id}", "DELETE", port=promptyoself_port)

    if result["success"]:
        return f"✅ Task {task_id} deleted successfully"
    else:
        return f"❌ Failed to delete task: {result['error']}"


# ============================================================================
# REMINDER MANAGEMENT TOOLS
# ============================================================================

def schedule_reminder(
    reminder_text: str,
    scheduled_for: str,
    task_id: int,
    process_name: str,
    recurrence: Optional[str] = None,
    agent_id: Optional[str] = "letta_agent",
    promptyoself_port: Optional[int] = None
) -> str:
    """
    Schedule a reminder using the PromptyoSelf internal API.

    This tool allows Letta agents to create reminders that will be processed
    by the PromptyoSelf system at the specified time.

    Args:
        reminder_text: The content of the reminder message (required, non-empty)
        scheduled_for: ISO 8601 datetime string when reminder should trigger
        task_id: ID of the task this reminder is associated with
        process_name: Process name for the reminder (required, non-empty)
        recurrence: Recurrence pattern (optional, e.g., "daily", "weekly") - Note: Not used by internal API
        agent_id: ID of the agent scheduling the reminder (default: "letta_agent")
        promptyoself_port: PromptyoSelf server port (default: 5000)

    Returns:
        String describing the result of the operation
    """
    # Validate required parameters
    if not reminder_text or not reminder_text.strip():
        return "❌ Error: Reminder text is required and cannot be empty"

    if not scheduled_for or not scheduled_for.strip():
        return "❌ Error: Scheduled time is required and cannot be empty"

    if not process_name or not process_name.strip():
        return "❌ Error: Process name is required and cannot be empty"

    if not task_id:
        return "❌ Error: Task ID is required"

    # Note: recurrence parameter is accepted for API compatibility but not used by internal API
    if recurrence:
        # Log that recurrence is not supported by internal API
        pass  # Could add logging here if needed

    # Validate datetime format
    try:
        datetime.fromisoformat(scheduled_for.replace('Z', '+00:00'))
    except ValueError:
        return f"❌ Error: Invalid datetime format. Expected ISO 8601 format (e.g., '2024-01-15T10:30:00Z'), got: {scheduled_for}"

    # Check if API key is configured
    api_key = os.getenv("INTERNAL_AGENT_API_KEY")
    if not api_key:
        return "❌ Error: INTERNAL_AGENT_API_KEY environment variable is not set"

    # Prepare request headers
    headers = {
        "Content-Type": "application/json",
        "X-Agent-API-Key": api_key
    }

    # Prepare request payload for internal API
    payload = {
        "agent_id": agent_id,
        "reminder_text": reminder_text.strip(),
        "scheduled_for": scheduled_for.strip(),
        "process_name": process_name.strip()
    }

    result = make_api_request("/api/internal/agents/reminders", "POST", payload, headers, promptyoself_port)

    if result["success"]:
        return f"✅ Reminder scheduled successfully for {scheduled_for}"
    else:
        return f"❌ Failed to schedule reminder: {result['error']}"


def create_reminder_via_api(
    message: str,
    next_run: str,
    task_id: int,
    process_name: str,
    recurrence: Optional[str] = None,
    promptyoself_port: Optional[int] = None
) -> str:
    """
    Create a reminder using the standard PromptyoSelf API.

    Args:
        message: The reminder message content
        next_run: ISO 8601 datetime string when reminder should trigger
        task_id: ID of the task this reminder is associated with
        process_name: Process name for the reminder
        recurrence: Recurrence pattern (optional)
        promptyoself_port: PromptyoSelf server port (default: 5000)

    Returns:
        String describing the result of the operation
    """
    if not message or not message.strip():
        return "❌ Error: Reminder message is required and cannot be empty"

    if not next_run or not next_run.strip():
        return "❌ Error: Next run time is required and cannot be empty"

    if not process_name or not process_name.strip():
        return "❌ Error: Process name is required and cannot be empty"

    if not task_id:
        return "❌ Error: Task ID is required"

    # Validate datetime format
    try:
        datetime.fromisoformat(next_run.replace('Z', '+00:00'))
    except ValueError:
        return f"❌ Error: Invalid datetime format. Expected ISO 8601 format, got: {next_run}"

    payload = {
        "message": message.strip(),
        "next_run": next_run.strip(),
        "task_id": task_id,
        "process_name": process_name.strip()
    }

    if recurrence:
        payload["recurrence"] = recurrence.strip()

    result = make_api_request("/api/self-prompts", "POST", payload, port=promptyoself_port)

    if result["success"]:
        reminder_data = result["data"]
        return f"✅ Reminder created successfully with ID {reminder_data['reminder']['id']}"
    else:
        return f"❌ Failed to create reminder: {result['error']}"


def list_reminders(
    promptyoself_port: Optional[int] = None
) -> str:
    """
    List all reminders from PromptyoSelf.

    Args:
        promptyoself_port: PromptyoSelf server port (default: 5000)

    Returns:
        String containing the list of reminders
    """
    result = make_api_request("/api/self-prompts", port=promptyoself_port)

    if result["success"]:
        data = result["data"]
        reminders = data.get("reminders", [])

        if not reminders:
            return "📋 No reminders found"

        reminder_list = []
        for reminder in reminders:
            status_emoji = "✅" if reminder.get("status") == "active" else "⏸️"
            recurrence_info = f" ({reminder['recurrence']})" if reminder.get('recurrence') else ""
            reminder_list.append(
                f"{status_emoji} ID: {reminder['id']} | {reminder['message'][:50]}... | "
                f"Next: {reminder['next_run']} | Task: {reminder.get('task_name', 'N/A')}{recurrence_info}"
            )

        return f"📋 Found {len(reminders)} reminder(s):\n" + "\n".join(reminder_list)
    else:
        return f"❌ Failed to list reminders: {result['error']}"


def get_reminder(
    reminder_id: int,
    promptyoself_port: Optional[int] = None
) -> str:
    """
    Get details of a specific reminder.

    Args:
        reminder_id: ID of the reminder to retrieve
        promptyoself_port: PromptyoSelf server port (default: 5000)

    Returns:
        String containing reminder details
    """
    result = make_api_request(f"/api/self-prompts/{reminder_id}", port=promptyoself_port)

    if result["success"]:
        reminder = result["data"]["reminder"]
        recurrence_info = f"\n• Recurrence: {reminder['recurrence']}" if reminder.get('recurrence') else ""
        return (
            f"📋 Reminder Details:\n"
            f"• ID: {reminder['id']}\n"
            f"• Message: {reminder['message']}\n"
            f"• Next Run: {reminder['next_run']}\n"
            f"• Status: {reminder['status']}\n"
            f"• Process: {reminder['process_name']}\n"
            f"• Task: {reminder.get('task_name', 'N/A')} (ID: {reminder['task_id']})\n"
            f"• Project: {reminder.get('project_name', 'N/A')}{recurrence_info}\n"
            f"• Created: {reminder['created_at']}\n"
            f"• Updated: {reminder['updated_at']}"
        )
    else:
        return f"❌ Failed to get reminder: {result['error']}"


def update_reminder(
    reminder_id: int,
    message: Optional[str] = None,
    next_run: Optional[str] = None,
    recurrence: Optional[str] = None,
    status: Optional[str] = None,
    process_name: Optional[str] = None,
    task_id: Optional[int] = None,
    promptyoself_port: Optional[int] = None
) -> str:
    """
    Update an existing reminder.

    Args:
        reminder_id: ID of the reminder to update
        message: New reminder message (optional)
        next_run: New next run time (optional)
        recurrence: New recurrence pattern (optional)
        status: New status (optional)
        process_name: New process name (optional)
        task_id: New task ID (optional)
        promptyoself_port: PromptyoSelf server port (default: 5000)

    Returns:
        String describing the result of the operation
    """
    if not any([message, next_run, recurrence is not None, status, process_name, task_id]):
        return "❌ Error: At least one field must be provided for update"

    payload = {}
    if message:
        payload["message"] = message.strip()
    if next_run:
        # Validate datetime format
        try:
            datetime.fromisoformat(next_run.replace('Z', '+00:00'))
            payload["next_run"] = next_run.strip()
        except ValueError:
            return f"❌ Error: Invalid datetime format. Expected ISO 8601 format, got: {next_run}"
    if recurrence is not None:
        payload["recurrence"] = recurrence.strip() if recurrence else None
    if status:
        payload["status"] = status.strip()
    if process_name:
        payload["process_name"] = process_name.strip()
    if task_id:
        payload["task_id"] = task_id

    result = make_api_request(f"/api/self-prompts/{reminder_id}", "PUT", payload, port=promptyoself_port)

    if result["success"]:
        return f"✅ Reminder {reminder_id} updated successfully"
    else:
        return f"❌ Failed to update reminder: {result['error']}"


def delete_reminder(
    reminder_id: int,
    promptyoself_port: Optional[int] = None
) -> str:
    """
    Delete a reminder from PromptyoSelf.

    Args:
        reminder_id: ID of the reminder to delete
        promptyoself_port: PromptyoSelf server port (default: 5000)

    Returns:
        String describing the result of the operation
    """
    result = make_api_request(f"/api/self-prompts/{reminder_id}", "DELETE", port=promptyoself_port)

    if result["success"]:
        return f"✅ Reminder {reminder_id} deleted successfully"
    else:
        return f"❌ Failed to delete reminder: {result['error']}"


# ============================================================================
# LETTA TOOL METADATA
# ============================================================================

# Tool metadata for Letta registration
LETTA_TOOLS_METADATA = [
    {
        "name": "create_project",
        "description": "Create a new project in PromptyoSelf for organizing tasks and reminders",
        "parameters": {
            "type": "object",
            "properties": {
                "name": {"type": "string", "description": "Project name (required)"},
                "description": {"type": "string", "description": "Project description (optional)"},
                "promptyoself_port": {"type": "integer", "description": "PromptyoSelf server port (default: 5000)"}
            },
            "required": ["name"]
        }
    },
    {
        "name": "list_projects",
        "description": "List projects from PromptyoSelf with optional filtering and pagination",
        "parameters": {
            "type": "object",
            "properties": {
                "name_filter": {"type": "string", "description": "Filter projects by name (optional)"},
                "page": {"type": "integer", "description": "Page number for pagination (default: 1)"},
                "per_page": {"type": "integer", "description": "Items per page (default: 10)"},
                "promptyoself_port": {"type": "integer", "description": "PromptyoSelf server port (default: 5000)"}
            },
            "required": []
        }
    },
    {
        "name": "get_project",
        "description": "Get detailed information about a specific project",
        "parameters": {
            "type": "object",
            "properties": {
                "project_id": {"type": "integer", "description": "ID of the project to retrieve"},
                "promptyoself_port": {"type": "integer", "description": "PromptyoSelf server port (default: 5000)"}
            },
            "required": ["project_id"]
        }
    },
    {
        "name": "update_project",
        "description": "Update an existing project's name or description",
        "parameters": {
            "type": "object",
            "properties": {
                "project_id": {"type": "integer", "description": "ID of the project to update"},
                "name": {"type": "string", "description": "New project name (optional)"},
                "description": {"type": "string", "description": "New project description (optional)"},
                "promptyoself_port": {"type": "integer", "description": "PromptyoSelf server port (default: 5000)"}
            },
            "required": ["project_id"]
        }
    },
    {
        "name": "delete_project",
        "description": "Delete a project from PromptyoSelf",
        "parameters": {
            "type": "object",
            "properties": {
                "project_id": {"type": "integer", "description": "ID of the project to delete"},
                "promptyoself_port": {"type": "integer", "description": "PromptyoSelf server port (default: 5000)"}
            },
            "required": ["project_id"]
        }
    },
    {
        "name": "create_task",
        "description": "Create a new task within a project, optionally as a subtask",
        "parameters": {
            "type": "object",
            "properties": {
                "name": {"type": "string", "description": "Task name (required)"},
                "project_id": {"type": "integer", "description": "ID of the project this task belongs to"},
                "description": {"type": "string", "description": "Task description (optional)"},
                "parent_task_id": {"type": "integer", "description": "ID of parent task for subtasks (optional)"},
                "promptyoself_port": {"type": "integer", "description": "PromptyoSelf server port (default: 5000)"}
            },
            "required": ["name", "project_id"]
        }
    },
    {
        "name": "list_tasks",
        "description": "List tasks from PromptyoSelf with optional filtering by name or project",
        "parameters": {
            "type": "object",
            "properties": {
                "name_filter": {"type": "string", "description": "Filter tasks by name (optional)"},
                "project_id_filter": {"type": "integer", "description": "Filter tasks by project ID (optional)"},
                "page": {"type": "integer", "description": "Page number for pagination (default: 1)"},
                "per_page": {"type": "integer", "description": "Items per page (default: 10)"},
                "promptyoself_port": {"type": "integer", "description": "PromptyoSelf server port (default: 5000)"}
            },
            "required": []
        }
    },
    {
        "name": "get_task",
        "description": "Get detailed information about a specific task",
        "parameters": {
            "type": "object",
            "properties": {
                "task_id": {"type": "integer", "description": "ID of the task to retrieve"},
                "promptyoself_port": {"type": "integer", "description": "PromptyoSelf server port (default: 5000)"}
            },
            "required": ["task_id"]
        }
    },
    {
        "name": "update_task",
        "description": "Update an existing task's properties",
        "parameters": {
            "type": "object",
            "properties": {
                "task_id": {"type": "integer", "description": "ID of the task to update"},
                "name": {"type": "string", "description": "New task name (optional)"},
                "description": {"type": "string", "description": "New task description (optional)"},
                "project_id": {"type": "integer", "description": "New project ID (optional)"},
                "parent_task_id": {"type": "integer", "description": "New parent task ID (optional)"},
                "promptyoself_port": {"type": "integer", "description": "PromptyoSelf server port (default: 5000)"}
            },
            "required": ["task_id"]
        }
    },
    {
        "name": "delete_task",
        "description": "Delete a task from PromptyoSelf",
        "parameters": {
            "type": "object",
            "properties": {
                "task_id": {"type": "integer", "description": "ID of the task to delete"},
                "promptyoself_port": {"type": "integer", "description": "PromptyoSelf server port (default: 5000)"}
            },
            "required": ["task_id"]
        }
    },
    {
        "name": "schedule_reminder",
        "description": "Schedule a reminder using the PromptyoSelf internal API (requires INTERNAL_AGENT_API_KEY)",
        "parameters": {
            "type": "object",
            "properties": {
                "reminder_text": {"type": "string", "description": "The content of the reminder message"},
                "scheduled_for": {"type": "string", "description": "ISO 8601 datetime string when reminder should trigger"},
                "task_id": {"type": "integer", "description": "ID of the task this reminder is associated with"},
                "process_name": {"type": "string", "description": "Process name for the reminder"},
                "agent_id": {"type": "string", "description": "ID of the agent scheduling the reminder (default: 'letta_agent')"},
                "promptyoself_port": {"type": "integer", "description": "PromptyoSelf server port (default: 5000)"}
            },
            "required": ["reminder_text", "scheduled_for", "task_id", "process_name"]
        }
    },
    {
        "name": "create_reminder_via_api",
        "description": "Create a reminder using the standard PromptyoSelf API",
        "parameters": {
            "type": "object",
            "properties": {
                "message": {"type": "string", "description": "The reminder message content"},
                "next_run": {"type": "string", "description": "ISO 8601 datetime string when reminder should trigger"},
                "task_id": {"type": "integer", "description": "ID of the task this reminder is associated with"},
                "process_name": {"type": "string", "description": "Process name for the reminder"},
                "recurrence": {"type": "string", "description": "Recurrence pattern (optional)"},
                "promptyoself_port": {"type": "integer", "description": "PromptyoSelf server port (default: 5000)"}
            },
            "required": ["message", "next_run", "task_id", "process_name"]
        }
    },
    {
        "name": "list_reminders",
        "description": "List all reminders from PromptyoSelf",
        "parameters": {
            "type": "object",
            "properties": {
                "promptyoself_port": {"type": "integer", "description": "PromptyoSelf server port (default: 5000)"}
            },
            "required": []
        }
    },
    {
        "name": "get_reminder",
        "description": "Get detailed information about a specific reminder",
        "parameters": {
            "type": "object",
            "properties": {
                "reminder_id": {"type": "integer", "description": "ID of the reminder to retrieve"},
                "promptyoself_port": {"type": "integer", "description": "PromptyoSelf server port (default: 5000)"}
            },
            "required": ["reminder_id"]
        }
    },
    {
        "name": "update_reminder",
        "description": "Update an existing reminder's properties",
        "parameters": {
            "type": "object",
            "properties": {
                "reminder_id": {"type": "integer", "description": "ID of the reminder to update"},
                "message": {"type": "string", "description": "New reminder message (optional)"},
                "next_run": {"type": "string", "description": "New next run time (optional)"},
                "recurrence": {"type": "string", "description": "New recurrence pattern (optional)"},
                "status": {"type": "string", "description": "New status (optional)"},
                "process_name": {"type": "string", "description": "New process name (optional)"},
                "task_id": {"type": "integer", "description": "New task ID (optional)"},
                "promptyoself_port": {"type": "integer", "description": "PromptyoSelf server port (default: 5000)"}
            },
            "required": ["reminder_id"]
        }
    },
    {
        "name": "delete_reminder",
        "description": "Delete a reminder from PromptyoSelf",
        "parameters": {
            "type": "object",
            "properties": {
                "reminder_id": {"type": "integer", "description": "ID of the reminder to delete"},
                "promptyoself_port": {"type": "integer", "description": "PromptyoSelf server port (default: 5000)"}
            },
            "required": ["reminder_id"]
        }
    }
]


# ============================================================================
# EXAMPLE USAGE AND TESTING
# ============================================================================

def main():
    """
    Example usage and testing function for the PromptyoSelf Letta tools.
    This demonstrates how the tools would be used in practice.
    """
    print("PromptyoSelf Letta Tools - Example Usage")
    print("=" * 50)

    # Check if API key is configured for internal API
    api_key = os.getenv("INTERNAL_AGENT_API_KEY")
    if api_key:
        print("✅ INTERNAL_AGENT_API_KEY is configured")
    else:
        print("⚠️  INTERNAL_AGENT_API_KEY is not set (required for schedule_reminder)")

    print("\n🔧 Available Tools:")
    for i, tool in enumerate(LETTA_TOOLS_METADATA, 1):
        print(f"{i:2d}. {tool['name']} - {tool['description']}")

    print(f"\n📊 Total Tools Available: {len(LETTA_TOOLS_METADATA)}")

    print("\n💡 Example Workflow:")
    print("1. create_project(name='AI Research', description='Research project for AI development')")
    print("2. create_task(name='Literature Review', project_id=1, description='Review recent papers')")
    print("3. schedule_reminder(reminder_text='Start literature review', scheduled_for='2024-01-15T09:00:00Z', task_id=1, process_name='research_workflow')")
    print("4. list_reminders() - to see all scheduled reminders")

    print("\n🚀 To use these tools with Letta:")
    print("1. Import this module in your Letta agent script")
    print("2. Register the tools using the LETTA_TOOLS_METADATA")
    print("3. Set INTERNAL_AGENT_API_KEY environment variable")
    print("4. Ensure PromptyoSelf server is running on the specified port")


if __name__ == "__main__":
    main()
